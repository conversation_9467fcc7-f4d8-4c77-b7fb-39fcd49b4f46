﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{827E0CD3-B72D-47B6-A68D-7590B98EB39B}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Common", "Common", "{BFF2DF6E-AA54-47C7-211F-79ACCBB4577C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ExchangeRateTracker.Common", "src\Common\ExchangeRateTracker.Common.csproj", "{49D498E1-E5A0-4341-BA8D-5CE8D61139C6}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "DatabaseMigration", "DatabaseMigration", "{8B1CFA1E-799B-ED62-66F0-4A31858DA834}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Services", "Services", "{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ExchangeRateTracker.DatabaseMigration", "src\DatabaseMigration\ExchangeRateTracker.DatabaseMigration.csproj", "{43F55487-B0F6-4FC2-AE3F-33416E7A93F9}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ExchangeRateTracker.CurrencyService", "src\CurrencyService\ExchangeRateTracker.CurrencyService.csproj", "{FAD89278-352B-49C2-AA82-0A684C75409D}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ExchangeRateTracker.UserService", "src\UserService\ExchangeRateTracker.UserService.csproj", "{14267B73-6796-47B0-B569-546FA03745FC}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ExchangeRateTracker.FinanceService", "src\FinanceService\ExchangeRateTracker.FinanceService.csproj", "{C69725DB-D273-470B-81E6-0D4CC7AEA6FE}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ExchangeRateTracker.ApiGateway", "src\ApiGateway\ExchangeRateTracker.ApiGateway.csproj", "{9F226773-A07A-4142-90DD-97B919B9EC6B}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tests", "tests", "{0AB3BF05-4346-4AA6-1389-037BE0695223}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ExchangeRateTracker.UserService.Tests", "tests\ExchangeRateTracker.UserService.Tests\ExchangeRateTracker.UserService.Tests.csproj", "{0413446C-7831-41F8-B4F8-1457650D33DB}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ExchangeRateTracker.FinanceService.Tests", "tests\ExchangeRateTracker.FinanceService.Tests\ExchangeRateTracker.FinanceService.Tests.csproj", "{D6E2B338-AE80-4233-851C-9F3A5963C218}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{49D498E1-E5A0-4341-BA8D-5CE8D61139C6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{49D498E1-E5A0-4341-BA8D-5CE8D61139C6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{49D498E1-E5A0-4341-BA8D-5CE8D61139C6}.Debug|x64.ActiveCfg = Debug|Any CPU
		{49D498E1-E5A0-4341-BA8D-5CE8D61139C6}.Debug|x64.Build.0 = Debug|Any CPU
		{49D498E1-E5A0-4341-BA8D-5CE8D61139C6}.Debug|x86.ActiveCfg = Debug|Any CPU
		{49D498E1-E5A0-4341-BA8D-5CE8D61139C6}.Debug|x86.Build.0 = Debug|Any CPU
		{49D498E1-E5A0-4341-BA8D-5CE8D61139C6}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{49D498E1-E5A0-4341-BA8D-5CE8D61139C6}.Release|Any CPU.Build.0 = Release|Any CPU
		{49D498E1-E5A0-4341-BA8D-5CE8D61139C6}.Release|x64.ActiveCfg = Release|Any CPU
		{49D498E1-E5A0-4341-BA8D-5CE8D61139C6}.Release|x64.Build.0 = Release|Any CPU
		{49D498E1-E5A0-4341-BA8D-5CE8D61139C6}.Release|x86.ActiveCfg = Release|Any CPU
		{49D498E1-E5A0-4341-BA8D-5CE8D61139C6}.Release|x86.Build.0 = Release|Any CPU
		{43F55487-B0F6-4FC2-AE3F-33416E7A93F9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{43F55487-B0F6-4FC2-AE3F-33416E7A93F9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{43F55487-B0F6-4FC2-AE3F-33416E7A93F9}.Debug|x64.ActiveCfg = Debug|Any CPU
		{43F55487-B0F6-4FC2-AE3F-33416E7A93F9}.Debug|x64.Build.0 = Debug|Any CPU
		{43F55487-B0F6-4FC2-AE3F-33416E7A93F9}.Debug|x86.ActiveCfg = Debug|Any CPU
		{43F55487-B0F6-4FC2-AE3F-33416E7A93F9}.Debug|x86.Build.0 = Debug|Any CPU
		{43F55487-B0F6-4FC2-AE3F-33416E7A93F9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{43F55487-B0F6-4FC2-AE3F-33416E7A93F9}.Release|Any CPU.Build.0 = Release|Any CPU
		{43F55487-B0F6-4FC2-AE3F-33416E7A93F9}.Release|x64.ActiveCfg = Release|Any CPU
		{43F55487-B0F6-4FC2-AE3F-33416E7A93F9}.Release|x64.Build.0 = Release|Any CPU
		{43F55487-B0F6-4FC2-AE3F-33416E7A93F9}.Release|x86.ActiveCfg = Release|Any CPU
		{43F55487-B0F6-4FC2-AE3F-33416E7A93F9}.Release|x86.Build.0 = Release|Any CPU
		{FAD89278-352B-49C2-AA82-0A684C75409D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FAD89278-352B-49C2-AA82-0A684C75409D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FAD89278-352B-49C2-AA82-0A684C75409D}.Debug|x64.ActiveCfg = Debug|Any CPU
		{FAD89278-352B-49C2-AA82-0A684C75409D}.Debug|x64.Build.0 = Debug|Any CPU
		{FAD89278-352B-49C2-AA82-0A684C75409D}.Debug|x86.ActiveCfg = Debug|Any CPU
		{FAD89278-352B-49C2-AA82-0A684C75409D}.Debug|x86.Build.0 = Debug|Any CPU
		{FAD89278-352B-49C2-AA82-0A684C75409D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FAD89278-352B-49C2-AA82-0A684C75409D}.Release|Any CPU.Build.0 = Release|Any CPU
		{FAD89278-352B-49C2-AA82-0A684C75409D}.Release|x64.ActiveCfg = Release|Any CPU
		{FAD89278-352B-49C2-AA82-0A684C75409D}.Release|x64.Build.0 = Release|Any CPU
		{FAD89278-352B-49C2-AA82-0A684C75409D}.Release|x86.ActiveCfg = Release|Any CPU
		{FAD89278-352B-49C2-AA82-0A684C75409D}.Release|x86.Build.0 = Release|Any CPU
		{14267B73-6796-47B0-B569-546FA03745FC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{14267B73-6796-47B0-B569-546FA03745FC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{14267B73-6796-47B0-B569-546FA03745FC}.Debug|x64.ActiveCfg = Debug|Any CPU
		{14267B73-6796-47B0-B569-546FA03745FC}.Debug|x64.Build.0 = Debug|Any CPU
		{14267B73-6796-47B0-B569-546FA03745FC}.Debug|x86.ActiveCfg = Debug|Any CPU
		{14267B73-6796-47B0-B569-546FA03745FC}.Debug|x86.Build.0 = Debug|Any CPU
		{14267B73-6796-47B0-B569-546FA03745FC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{14267B73-6796-47B0-B569-546FA03745FC}.Release|Any CPU.Build.0 = Release|Any CPU
		{14267B73-6796-47B0-B569-546FA03745FC}.Release|x64.ActiveCfg = Release|Any CPU
		{14267B73-6796-47B0-B569-546FA03745FC}.Release|x64.Build.0 = Release|Any CPU
		{14267B73-6796-47B0-B569-546FA03745FC}.Release|x86.ActiveCfg = Release|Any CPU
		{14267B73-6796-47B0-B569-546FA03745FC}.Release|x86.Build.0 = Release|Any CPU
		{C69725DB-D273-470B-81E6-0D4CC7AEA6FE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C69725DB-D273-470B-81E6-0D4CC7AEA6FE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C69725DB-D273-470B-81E6-0D4CC7AEA6FE}.Debug|x64.ActiveCfg = Debug|Any CPU
		{C69725DB-D273-470B-81E6-0D4CC7AEA6FE}.Debug|x64.Build.0 = Debug|Any CPU
		{C69725DB-D273-470B-81E6-0D4CC7AEA6FE}.Debug|x86.ActiveCfg = Debug|Any CPU
		{C69725DB-D273-470B-81E6-0D4CC7AEA6FE}.Debug|x86.Build.0 = Debug|Any CPU
		{C69725DB-D273-470B-81E6-0D4CC7AEA6FE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C69725DB-D273-470B-81E6-0D4CC7AEA6FE}.Release|Any CPU.Build.0 = Release|Any CPU
		{C69725DB-D273-470B-81E6-0D4CC7AEA6FE}.Release|x64.ActiveCfg = Release|Any CPU
		{C69725DB-D273-470B-81E6-0D4CC7AEA6FE}.Release|x64.Build.0 = Release|Any CPU
		{C69725DB-D273-470B-81E6-0D4CC7AEA6FE}.Release|x86.ActiveCfg = Release|Any CPU
		{C69725DB-D273-470B-81E6-0D4CC7AEA6FE}.Release|x86.Build.0 = Release|Any CPU
		{9F226773-A07A-4142-90DD-97B919B9EC6B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9F226773-A07A-4142-90DD-97B919B9EC6B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9F226773-A07A-4142-90DD-97B919B9EC6B}.Debug|x64.ActiveCfg = Debug|Any CPU
		{9F226773-A07A-4142-90DD-97B919B9EC6B}.Debug|x64.Build.0 = Debug|Any CPU
		{9F226773-A07A-4142-90DD-97B919B9EC6B}.Debug|x86.ActiveCfg = Debug|Any CPU
		{9F226773-A07A-4142-90DD-97B919B9EC6B}.Debug|x86.Build.0 = Debug|Any CPU
		{9F226773-A07A-4142-90DD-97B919B9EC6B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9F226773-A07A-4142-90DD-97B919B9EC6B}.Release|Any CPU.Build.0 = Release|Any CPU
		{9F226773-A07A-4142-90DD-97B919B9EC6B}.Release|x64.ActiveCfg = Release|Any CPU
		{9F226773-A07A-4142-90DD-97B919B9EC6B}.Release|x64.Build.0 = Release|Any CPU
		{9F226773-A07A-4142-90DD-97B919B9EC6B}.Release|x86.ActiveCfg = Release|Any CPU
		{9F226773-A07A-4142-90DD-97B919B9EC6B}.Release|x86.Build.0 = Release|Any CPU
		{0413446C-7831-41F8-B4F8-1457650D33DB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0413446C-7831-41F8-B4F8-1457650D33DB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0413446C-7831-41F8-B4F8-1457650D33DB}.Debug|x64.ActiveCfg = Debug|Any CPU
		{0413446C-7831-41F8-B4F8-1457650D33DB}.Debug|x64.Build.0 = Debug|Any CPU
		{0413446C-7831-41F8-B4F8-1457650D33DB}.Debug|x86.ActiveCfg = Debug|Any CPU
		{0413446C-7831-41F8-B4F8-1457650D33DB}.Debug|x86.Build.0 = Debug|Any CPU
		{0413446C-7831-41F8-B4F8-1457650D33DB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0413446C-7831-41F8-B4F8-1457650D33DB}.Release|Any CPU.Build.0 = Release|Any CPU
		{0413446C-7831-41F8-B4F8-1457650D33DB}.Release|x64.ActiveCfg = Release|Any CPU
		{0413446C-7831-41F8-B4F8-1457650D33DB}.Release|x64.Build.0 = Release|Any CPU
		{0413446C-7831-41F8-B4F8-1457650D33DB}.Release|x86.ActiveCfg = Release|Any CPU
		{0413446C-7831-41F8-B4F8-1457650D33DB}.Release|x86.Build.0 = Release|Any CPU
		{D6E2B338-AE80-4233-851C-9F3A5963C218}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D6E2B338-AE80-4233-851C-9F3A5963C218}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D6E2B338-AE80-4233-851C-9F3A5963C218}.Debug|x64.ActiveCfg = Debug|Any CPU
		{D6E2B338-AE80-4233-851C-9F3A5963C218}.Debug|x64.Build.0 = Debug|Any CPU
		{D6E2B338-AE80-4233-851C-9F3A5963C218}.Debug|x86.ActiveCfg = Debug|Any CPU
		{D6E2B338-AE80-4233-851C-9F3A5963C218}.Debug|x86.Build.0 = Debug|Any CPU
		{D6E2B338-AE80-4233-851C-9F3A5963C218}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D6E2B338-AE80-4233-851C-9F3A5963C218}.Release|Any CPU.Build.0 = Release|Any CPU
		{D6E2B338-AE80-4233-851C-9F3A5963C218}.Release|x64.ActiveCfg = Release|Any CPU
		{D6E2B338-AE80-4233-851C-9F3A5963C218}.Release|x64.Build.0 = Release|Any CPU
		{D6E2B338-AE80-4233-851C-9F3A5963C218}.Release|x86.ActiveCfg = Release|Any CPU
		{D6E2B338-AE80-4233-851C-9F3A5963C218}.Release|x86.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{BFF2DF6E-AA54-47C7-211F-79ACCBB4577C} = {827E0CD3-B72D-47B6-A68D-7590B98EB39B}
		{49D498E1-E5A0-4341-BA8D-5CE8D61139C6} = {BFF2DF6E-AA54-47C7-211F-79ACCBB4577C}
		{8B1CFA1E-799B-ED62-66F0-4A31858DA834} = {827E0CD3-B72D-47B6-A68D-7590B98EB39B}
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890} = {827E0CD3-B72D-47B6-A68D-7590B98EB39B}
		{43F55487-B0F6-4FC2-AE3F-33416E7A93F9} = {8B1CFA1E-799B-ED62-66F0-4A31858DA834}
		{FAD89278-352B-49C2-AA82-0A684C75409D} = {A1B2C3D4-E5F6-7890-ABCD-EF1234567890}
		{14267B73-6796-47B0-B569-546FA03745FC} = {A1B2C3D4-E5F6-7890-ABCD-EF1234567890}
		{C69725DB-D273-470B-81E6-0D4CC7AEA6FE} = {A1B2C3D4-E5F6-7890-ABCD-EF1234567890}
		{9F226773-A07A-4142-90DD-97B919B9EC6B} = {A1B2C3D4-E5F6-7890-ABCD-EF1234567890}
		{0413446C-7831-41F8-B4F8-1457650D33DB} = {0AB3BF05-4346-4AA6-1389-037BE0695223}
		{D6E2B338-AE80-4233-851C-9F3A5963C218} = {0AB3BF05-4346-4AA6-1389-037BE0695223}
	EndGlobalSection
EndGlobal
